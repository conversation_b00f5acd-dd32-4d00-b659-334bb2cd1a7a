<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#45A049"
                android:endColor="#2E7D32"
                android:angle="135" />
            <corners android:radius="25dp" />
        </shape>
    </item>
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#CCCCCC"
                android:endColor="#999999"
                android:angle="135" />
            <corners android:radius="25dp" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="#4CAF50"
                android:endColor="#388E3C"
                android:angle="135" />
            <corners android:radius="25dp" />
        </shape>
    </item>
</selector>
