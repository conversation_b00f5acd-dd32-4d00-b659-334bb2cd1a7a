<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffffff"
    android:orientation="vertical">

    <LinearLayout
        android:layout_above="@+id/btm1"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/primarymain"
            android:paddingTop="28dp">

            <ImageView
                android:id="@+id/menu_popup"
                android:layout_width="35dp"
                android:layout_height="35dp"
                android:layout_alignParentRight="true"
                android:layout_marginEnd="10dp"
                android:padding="6dp"
                android:src="@drawable/dots" />
        </RelativeLayout>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="290dp"
                    android:backgroundTint="@color/primarymain"
                    android:background="@drawable/home_square"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="145dp">

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentBottom="true"
                            android:layout_centerHorizontal="true"
                            android:layout_marginBottom="20dp"
                            android:orientation="vertical">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:text="VPN Protection"
                                android:textColor="#ffffff"
                                android:textSize="32sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center_horizontal"
                                android:layout_marginTop="6dp"
                                android:text="Secure Your Connection"
                                android:textColor="#ffffff"
                                android:textSize="20sp" />
                        </LinearLayout>
                    </RelativeLayout>

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="150dp"
                            android:orientation="vertical"
                            android:weightSum="4">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="0dp"
                                android:layout_weight="2" />

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="0dp"
                                android:layout_weight="2" />
                        </LinearLayout>

                        <!-- Beautiful VPN Controls Section -->
                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_centerInParent="true"
                            android:padding="25dp">

                            <!-- VPN Card Container -->
                            <androidx.cardview.widget.CardView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_centerInParent="true"
                                app:cardCornerRadius="20dp"
                                app:cardElevation="12dp"
                                android:layout_margin="5dp"
                                android:background="@drawable/vpn_card_gradient"
                                app:cardBackgroundColor="@android:color/transparent">

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:orientation="vertical"
                                    android:padding="30dp"
                                    android:gravity="center"
                                    android:background="@drawable/vpn_card_gradient">

                                    <!-- VPN Shield Icon -->
                                    <ImageView
                                        android:layout_width="80dp"
                                        android:layout_height="80dp"
                                        android:src="@drawable/ic_vpn_shield"
                                        android:layout_marginBottom="20dp"
                                        app:tint="#4CAF50"
                                        android:elevation="4dp" />

                                    <!-- Country Selection Card -->
                                    <androidx.cardview.widget.CardView
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        app:cardCornerRadius="18dp"
                                        app:cardElevation="6dp"
                                        android:layout_marginBottom="25dp"
                                        app:cardBackgroundColor="#FFFFFF">

                                        <LinearLayout
                                            android:layout_width="match_parent"
                                            android:layout_height="wrap_content"
                                            android:orientation="horizontal"
                                            android:gravity="center_vertical"
                                            android:padding="18dp">

                                            <TextView
                                                android:layout_width="wrap_content"
                                                android:layout_height="wrap_content"
                                                android:text="🌍"
                                                android:textSize="32sp"
                                                android:layout_marginEnd="18dp"
                                                android:elevation="2dp" />

                                            <LinearLayout
                                                android:layout_width="0dp"
                                                android:layout_height="wrap_content"
                                                android:layout_weight="1"
                                                android:orientation="vertical">

                                                <TextView
                                                    android:layout_width="wrap_content"
                                                    android:layout_height="wrap_content"
                                                    android:text="Select Location"
                                                    android:textSize="13sp"
                                                    android:textColor="#555555"
                                                    android:textStyle="bold"
                                                    android:fontFamily="sans-serif-medium" />

                                                <Spinner
                                                    android:id="@+id/spinner_countries"
                                                    android:layout_width="match_parent"
                                                    android:layout_height="40dp"
                                                    android:layout_marginTop="8dp"
                                                    android:background="@android:color/transparent"
                                                    android:textSize="16sp" />

                                            </LinearLayout>

                                            <ImageView
                                                android:layout_width="24dp"
                                                android:layout_height="24dp"
                                                android:src="@drawable/ic_arrow_drop_down"
                                                app:tint="#888888" />

                                        </LinearLayout>

                                    </androidx.cardview.widget.CardView>

                                    <!-- Connection Status -->
                                    <LinearLayout
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:orientation="horizontal"
                                        android:gravity="center"
                                        android:layout_marginBottom="30dp"
                                        android:padding="8dp">

                                        <View
                                            android:id="@+id/status_indicator"
                                            android:layout_width="14dp"
                                            android:layout_height="14dp"
                                            android:background="@drawable/status_indicator_disconnected"
                                            android:layout_marginEnd="12dp"
                                            android:layout_gravity="center_vertical"
                                            android:elevation="2dp" />

                                        <TextView
                                            android:id="@+id/tv_vpn_connection_status"
                                            android:layout_width="wrap_content"
                                            android:layout_height="wrap_content"
                                            android:text="Not Connected"
                                            android:textColor="#FF5722"
                                            android:textSize="17sp"
                                            android:textStyle="bold"
                                            android:fontFamily="sans-serif-medium" />

                                    </LinearLayout>

                                    <!-- Beautiful Secure Button -->
                                    <Button
                                        android:id="@+id/btn_secure"
                                        android:layout_width="match_parent"
                                        android:layout_height="60dp"
                                        android:text="🔒 SECURE NOW"
                                        android:textColor="#FFFFFF"
                                        android:textSize="18sp"
                                        android:textStyle="bold"
                                        android:background="@drawable/secure_button_gradient"
                                        android:letterSpacing="0.1"
                                        android:fontFamily="sans-serif-medium"
                                        android:elevation="8dp"
                                        android:stateListAnimator="@null" />

                                </LinearLayout>

                            </androidx.cardview.widget.CardView>

                        </RelativeLayout>
                    </RelativeLayout>
                </LinearLayout>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="20dp"
                    android:layout_marginBottom="20dp"
                    android:text="Keep Your Data Safe"
                    android:textColor="#000000"
                    android:textSize="24sp"
                    android:textStyle="bold" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#d9d9d9" />

                <RelativeLayout
                    android:id="@+id/RL_Traking"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginBottom="10dp"
                    android:padding="10dp">

                    <ImageView
                        android:id="@+id/icImage"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_centerVertical="true"
                        android:src="@drawable/hm_traking_icon" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="20dp"
                        android:layout_marginRight="20dp"
                        android:layout_toStartOf="@+id/icNext"
                        android:layout_toEndOf="@+id/icImage"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Who is tracking your device"
                            android:textColor="#000000" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Check app access"
                            android:textColor="#a3a3a3" />
                    </LinearLayout>

                    <ImageView
                        android:id="@+id/icNext"
                        android:layout_width="21dp"
                        android:layout_height="21dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:src="@drawable/hm_next_icon" />
                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#d9d9d9" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="10dp"
                    android:background="#f3f3f3" />

                <RelativeLayout
                    android:id="@+id/RL_Network"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginBottom="10dp"
                    android:padding="10dp">

                    <ImageView
                        android:id="@+id/icImage2"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_centerVertical="true"
                        android:src="@drawable/hm_network_icon" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="20dp"
                        android:layout_marginRight="20dp"
                        android:layout_toStartOf="@+id/icNext2"
                        android:layout_toEndOf="@+id/icImage2"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Network"
                            android:textColor="#000000" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Access your device network"
                            android:textColor="#a3a3a3" />
                    </LinearLayout>

                    <ImageView
                        android:id="@+id/icNext2"
                        android:layout_width="21dp"
                        android:layout_height="21dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:src="@drawable/hm_next_icon" />
                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#d9d9d9" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="10dp"
                    android:background="#f3f3f3" />

                <RelativeLayout
                    android:id="@+id/RL_Device_Info"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginBottom="10dp"
                    android:padding="10dp">

                    <ImageView
                        android:id="@+id/icImage3"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_centerVertical="true"
                        android:src="@drawable/hm_device_icon" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="20dp"
                        android:layout_marginRight="20dp"
                        android:layout_toStartOf="@+id/icNext3"
                        android:layout_toEndOf="@+id/icImage3"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Device Information"
                            android:textColor="#000000" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Check device important information"
                            android:textColor="#a3a3a3" />
                    </LinearLayout>

                    <ImageView
                        android:id="@+id/icNext3"
                        android:layout_width="21dp"
                        android:layout_height="21dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:src="@drawable/hm_next_icon" />
                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#d9d9d9" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="10dp"
                    android:background="#f3f3f3" />

                <!-- VPN Protection Section -->
                <RelativeLayout
                    android:id="@+id/RL_VPN"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginBottom="10dp"
                    android:padding="10dp">

                    <ImageView
                        android:id="@+id/icImage4"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_centerVertical="true"
                        android:src="@drawable/hm_vpn_icon" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="20dp"
                        android:layout_marginRight="20dp"
                        android:layout_toStartOf="@+id/vpn_status"
                        android:layout_toEndOf="@+id/icImage4"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="VPN Protection"
                            android:textColor="#000000" />

                        <TextView
                            android:id="@+id/tv_vpn_status"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Secure your connection"
                            android:textColor="#a3a3a3" />
                    </LinearLayout>

                    <ImageView
                        android:id="@+id/vpn_status"
                        android:layout_width="21dp"
                        android:layout_height="21dp"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:src="@drawable/hm_next_icon" />
                </RelativeLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#d9d9d9" />
            </LinearLayout>
        </ScrollView>

    </LinearLayout>

    <RelativeLayout
        android:id="@+id/btm1"
        android:layout_width="match_parent"
        android:layout_height="70dp"
        android:gravity="center"
        android:layout_alignParentBottom="true">

        <include
            android:id="@+id/reguler_banner_ad"
            layout="@layout/ads_layout_banner"
            android:visibility="visible" />

    </RelativeLayout>

</RelativeLayout>

