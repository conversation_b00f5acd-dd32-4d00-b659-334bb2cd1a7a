<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/bg_gradient"
    android:padding="16dp">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="24dp">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_vpn_shield"
            android:tint="@android:color/white" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="VPN Protection"
            android:textColor="@android:color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            android:layout_marginStart="12dp" />

    </LinearLayout>

    <!-- Connection Status Card -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="8dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_connection_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Disconnected"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@android:color/holo_red_dark"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/tv_selected_server"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Selected: United States"
                android:textSize="14sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginBottom="16dp" />

            <Button
                android:id="@+id/btn_connect"
                android:layout_width="200dp"
                android:layout_height="50dp"
                android:text="Connect"
                android:textColor="@android:color/white"
                android:textStyle="bold"
                android:background="@drawable/button_rounded"
                android:backgroundTint="@android:color/holo_green_light" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Server Selection -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Select Server:"
        android:textColor="@android:color/white"
        android:textSize="16sp"
        android:textStyle="bold"
        android:layout_marginBottom="12dp" />

    <!-- Server List -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        app:cardCornerRadius="12dp"
        app:cardElevation="8dp">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_servers"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="8dp" />

    </androidx.cardview.widget.CardView>

    <!-- Ad Banner -->
    <com.google.android.gms.ads.AdView
        android:id="@+id/adView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:adSize="BANNER"
        app:adUnitId="ca-app-pub-3940256099942544/6300978111" />

</LinearLayout>
