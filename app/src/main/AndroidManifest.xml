<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.demo.trackdevice">



    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.BLUETOOTH"/>
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN"/>
    <uses-permission android:name="android.permission.WRITE_SETTINGS"/>
    <uses-permission android:name="android.permission.GET_PERMISSIONS"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <queries>
        <intent>
            <action android:name="android.intent.action.MAIN"/>
        </intent>
    </queries>
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"/>
    <uses-permission android:name="android.permission.REQUEST_DELETE_PACKAGES"/>

    <!-- VPN Permissions -->
    <uses-permission android:name="android.permission.BIND_VPN_SERVICE"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>

    <permission android:name="com.demo.trackdevice.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" android:protectionLevel="signature"/>
    <uses-permission android:name="com.demo.trackdevice.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"/>

    <application
        android:name="com.demo.trackdevice.ads.MyApplication"
        android:requestLegacyExternalStorage="true"
        android:hardwareAccelerated="true"
        android:usesCleartextTraffic="true"
        android:allowBackup="true"
        android:largeHeap="true"
        android:icon="@drawable/logo"
        android:label="@string/app_name"
        android:roundIcon="@drawable/logo"
        android:supportsRtl="true"
        android:theme="@style/MonitoringTool">

        <meta-data
            android:name="com.google.android.gms.ads.AD_MANAGER_APP"
            android:value="true" />
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-3940256099942544~3347511713"/>
        <meta-data
            android:name="applovin.sdk.key"
            android:value="@string/applovin_sdk_key" />


        <activity
            android:exported="true"
            android:name="com.demo.trackdevice.splashAds.SplashActivity"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.demo.trackdevice.splashAds.PrivacyTermsActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.demo.trackdevice.activity.MainActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.demo.trackdevice.activity.Access_Permission_Activity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.demo.trackdevice.activity.Camera_Permission_Activity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.demo.trackdevice.activity.Location_Permission_Activity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.demo.trackdevice.activity.Access_Network_Activity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.demo.trackdevice.activity.Network_Permission_Activity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.demo.trackdevice.activity.Network_Security_Activity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.demo.trackdevice.activity.Device_Info_Activity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.demo.trackdevice.activity.Storage_Permission_Activity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.demo.trackdevice.activity.Contact_Permission_Activity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.demo.trackdevice.activity.Mic_Permission_Activity"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.demo.trackdevice.activity.VpnActivity"
            android:screenOrientation="portrait" />

    </application>

</manifest>