package com.demo.trackdevice.activity;

import android.app.Activity;
import android.app.Dialog;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.PopupMenu;

import com.demo.trackdevice.utils.AppPreferences;
import com.demo.trackdevice.R;
import com.demo.trackdevice.utils.SafetyCalculation;
import com.demo.trackdevice.ads.AdsCommon;
import com.demo.trackdevice.ads.MyApplication;
import com.github.lzyzsd.circleprogress.CircleProgress;

public class MainActivity extends AppCompatActivity {
    AppPreferences appPreferences;
    CircleProgress circle_progress;
    TextView progress_percentage;

    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        getWindow().getDecorView().setSystemUiVisibility(1280);
        setWindowFlag(this, 67108864, false);
        getWindow().setStatusBarColor(0);
        setContentView(R.layout.activity_main);
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }

        //Reguler Banner Ads
        RelativeLayout admob_banner = (RelativeLayout) findViewById(R.id.Admob_Banner_Frame);
        LinearLayout adContainer = (LinearLayout) findViewById(R.id.banner_container);
        FrameLayout qureka = (FrameLayout) findViewById(R.id.qureka);
        AdsCommon.RegulerBanner(this, admob_banner, adContainer, qureka);


        final ImageView imageView = (ImageView) findViewById(R.id.menu_popup);
        imageView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                PopupMenu popupMenu = new PopupMenu(MainActivity.this, imageView);
                popupMenu.getMenuInflater().inflate(R.menu.main_home, popupMenu.getMenu());
                popupMenu.setOnMenuItemClickListener(new PopupMenu.OnMenuItemClickListener() {
                    @Override
                    public boolean onMenuItemClick(MenuItem menuItem) {
                        if (menuItem.getTitle().equals("Rate App")) {
                            MainActivity.this.startActivity(new Intent("android.intent.action.VIEW", Uri.parse("market://details?id=" + MainActivity.this.getApplicationContext().getPackageName())));
                            return true;
                        } else if (menuItem.getTitle().equals("Share App")) {
                            Intent intent = new Intent("android.intent.action.SEND");
                            intent.setFlags(268435456);
                            intent.setType("text/plain");
                            intent.putExtra("android.intent.extra.TEXT", "Hey, Download best app : https://play.google.com/store/apps/details?id=" + MainActivity.this.getApplicationContext().getPackageName());
                            MainActivity.this.startActivity(intent);
                            return true;
                        } else if (!menuItem.getTitle().equals("Privacy Policy")) {
                            return true;
                        } else {
                            Intent intentPrivacy = new Intent(Intent.ACTION_VIEW, Uri.parse(MyApplication.PrivacyPolicy));
                            intentPrivacy.setPackage("com.android.chrome");
                            startActivity(intentPrivacy);
                            return true;
                        }
                    }
                });
                popupMenu.show();
            }
        });
        this.appPreferences = new AppPreferences(getApplicationContext());
        this.circle_progress = (CircleProgress) findViewById(R.id.circle_progress);
        this.progress_percentage = (TextView) findViewById(R.id.progress_percentage);
        ((RelativeLayout) findViewById(R.id.RL_Traking)).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(MainActivity.this, Access_Permission_Activity.class);
                AdsCommon.InterstitialAd(MainActivity.this, intent);
            }
        });
        ((RelativeLayout) findViewById(R.id.RL_Network)).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(MainActivity.this, Access_Network_Activity.class);
                AdsCommon.InterstitialAd(MainActivity.this, intent);
            }
        });
        ((RelativeLayout) findViewById(R.id.RL_Device_Info)).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(MainActivity.this, Device_Info_Activity.class);
                AdsCommon.InterstitialAd(MainActivity.this, intent);
            }
        });
        ((TextView) findViewById(R.id.tvDeviceModel)).setText(Build.BRAND.toUpperCase());
        ((TextView) findViewById(R.id.tvVersionNo)).setText("Android " + Build.VERSION.RELEASE);
    }

    public static void setWindowFlag(Activity activity, int i, boolean z) {
        Window window = activity.getWindow();
        WindowManager.LayoutParams attributes = window.getAttributes();
        if (z) {
            attributes.flags = i | attributes.flags;
        } else {
            attributes.flags = (~i) & attributes.flags;
        }
        window.setAttributes(attributes);
    }

    @Override
    public void onResume() {
        super.onResume();
        int SafetyCalculationCount = new SafetyCalculation().SafetyCalculationCount(this);
        this.circle_progress.setProgress(SafetyCalculationCount);
        this.progress_percentage.setText(SafetyCalculationCount + "%");
    }

    @Override
    public void onBackPressed() {
        ExitDialog();
    }

    private void ExitDialog() {

        final Dialog dialog = new Dialog(MainActivity.this, R.style.DialogTheme);
        dialog.setContentView(R.layout.popup_exit_dialog);
        dialog.setCancelable(false);

        RelativeLayout no = (RelativeLayout) dialog.findViewById(R.id.no);
        RelativeLayout rate = (RelativeLayout) dialog.findViewById(R.id.rate);
        RelativeLayout yes = (RelativeLayout) dialog.findViewById(R.id.yes);

        no.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });

        rate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                final String rateapp = getPackageName();
                Intent intent1 = new Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id=" + rateapp));
                startActivity(intent1);
            }
        });

        yes.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                finish();
                System.exit(0);
                //Intent intent = new Intent(AppMainHomeActivity.this, AppThankYouActivity.class);
                //intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                //AdsCommon.InterstitialAd(AppMainHomeActivity.this, intent);
            }
        });

        dialog.show();
    }

}
