package com.demo.trackdevice.activity;

import android.app.Activity;
import android.app.Dialog;
import android.content.Intent;
import android.net.Uri;
import android.net.VpnService;
import android.os.Build;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.AdapterView;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.PopupMenu;

import com.demo.trackdevice.utils.AppPreferences;
import com.demo.trackdevice.R;
import com.demo.trackdevice.utils.SafetyCalculation;
import com.demo.trackdevice.ads.AdsCommon;
import com.demo.trackdevice.ads.MyApplication;
import com.demo.trackdevice.ads.VpnAdManager;
import com.demo.trackdevice.vpn.VpnManager;
import com.demo.trackdevice.vpn.adapter.CountrySpinnerAdapter;
import com.demo.trackdevice.vpn.model.Countries;
import com.github.lzyzsd.circleprogress.CircleProgress;

import java.util.List;

public class MainActivity extends AppCompatActivity implements VpnManager.VpnConnectionListener {
    private static final int VPN_REQUEST_CODE = 1001;

    AppPreferences appPreferences;
    VpnManager vpnManager;
    VpnAdManager vpnAdManager;

    // VPN Controls
    Spinner spinnerCountries;
    TextView tvVpnConnectionStatus;
    Button btnSecure;
    View statusIndicator;
    CountrySpinnerAdapter countryAdapter;
    Countries selectedCountry;

    @Override
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        getWindow().getDecorView().setSystemUiVisibility(1280);
        setWindowFlag(this, 67108864, false);
        getWindow().setStatusBarColor(0);
        setContentView(R.layout.activity_main);
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }

        //Reguler Banner Ads
        RelativeLayout admob_banner = (RelativeLayout) findViewById(R.id.Admob_Banner_Frame);
        LinearLayout adContainer = (LinearLayout) findViewById(R.id.banner_container);
        FrameLayout qureka = (FrameLayout) findViewById(R.id.qureka);
        AdsCommon.RegulerBanner(this, admob_banner, adContainer, qureka);


        final ImageView imageView = (ImageView) findViewById(R.id.menu_popup);
        imageView.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                PopupMenu popupMenu = new PopupMenu(MainActivity.this, imageView);
                popupMenu.getMenuInflater().inflate(R.menu.main_home, popupMenu.getMenu());
                popupMenu.setOnMenuItemClickListener(new PopupMenu.OnMenuItemClickListener() {
                    @Override
                    public boolean onMenuItemClick(MenuItem menuItem) {
                        if (menuItem.getTitle().equals("Rate App")) {
                            MainActivity.this.startActivity(new Intent("android.intent.action.VIEW", Uri.parse("market://details?id=" + MainActivity.this.getApplicationContext().getPackageName())));
                            return true;
                        } else if (menuItem.getTitle().equals("Share App")) {
                            Intent intent = new Intent("android.intent.action.SEND");
                            intent.setFlags(268435456);
                            intent.setType("text/plain");
                            intent.putExtra("android.intent.extra.TEXT", "Hey, Download best app : https://play.google.com/store/apps/details?id=" + MainActivity.this.getApplicationContext().getPackageName());
                            MainActivity.this.startActivity(intent);
                            return true;
                        } else if (!menuItem.getTitle().equals("Privacy Policy")) {
                            return true;
                        } else {
                            Intent intentPrivacy = new Intent(Intent.ACTION_VIEW, Uri.parse(MyApplication.PrivacyPolicy));
                            intentPrivacy.setPackage("com.android.chrome");
                            startActivity(intentPrivacy);
                            return true;
                        }
                    }
                });
                popupMenu.show();
            }
        });
        this.appPreferences = new AppPreferences(getApplicationContext());
        this.vpnManager = new VpnManager(this);
        this.vpnManager.setConnectionListener(this);
        this.vpnAdManager = new VpnAdManager(this);

        // Initialize VPN controls
        initVpnControls();
        findViewById(R.id.RL_Traking).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(MainActivity.this, Access_Permission_Activity.class);
                AdsCommon.InterstitialAd(MainActivity.this, intent);
            }
        });
        findViewById(R.id.RL_Network).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(MainActivity.this, Access_Network_Activity.class);
                AdsCommon.InterstitialAd(MainActivity.this, intent);
            }
        });
        findViewById(R.id.RL_Device_Info).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Intent intent = new Intent(MainActivity.this, Device_Info_Activity.class);
                AdsCommon.InterstitialAd(MainActivity.this, intent);
            }
        });

        // Device info is now replaced with VPN controls
    }

    private void initVpnControls() {
        try {
            spinnerCountries = findViewById(R.id.spinner_countries);
            tvVpnConnectionStatus = findViewById(R.id.tv_vpn_connection_status);
            btnSecure = findViewById(R.id.btn_secure);
            statusIndicator = findViewById(R.id.status_indicator);

            // Setup country spinner
            List<Countries> countries = vpnManager.getAvailableServers();
            if (countries == null || countries.isEmpty()) {
                Toast.makeText(this, "No VPN servers available", Toast.LENGTH_SHORT).show();
                return;
            }

            countryAdapter = new CountrySpinnerAdapter(this, countries);
            spinnerCountries.setAdapter(countryAdapter);

            // Set default selection
            selectedCountry = countries.get(0);

            // Spinner selection listener
            spinnerCountries.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                    if (position >= 0 && position < countries.size()) {
                        selectedCountry = countries.get(position);
                    }
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) {
                    // Do nothing
                }
            });

            // Secure button click listener
            btnSecure.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    try {
                        // Add click animation
                        Animation clickAnimation = AnimationUtils.loadAnimation(MainActivity.this, R.anim.button_click_animation);
                        v.startAnimation(clickAnimation);

                        if (vpnManager.isVpnConnected()) {
                            disconnectVpn();
                        } else {
                            connectVpn();
                        }
                    } catch (Exception e) {
                        Toast.makeText(MainActivity.this, "Error: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                    }
                }
            });

            updateVpnUI();
        } catch (Exception e) {
            Toast.makeText(this, "Error initializing VPN controls: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private void connectVpn() {
        if (selectedCountry != null) {
            // Show ad before connecting to VPN
            showVpnAdAndConnect();
        } else {
            Toast.makeText(this, "Please select a country", Toast.LENGTH_SHORT).show();
        }
    }

    private void showVpnAdAndConnect() {
        vpnAdManager.showVpnAd(new VpnAdManager.VpnAdCallback() {
            @Override
            public void onAdCompleted() {
                // Ad completed, proceed with VPN connection
                proceedWithVpnConnection();
            }

            @Override
            public void onAdSkipped() {
                // Ad skipped, proceed with VPN connection
                proceedWithVpnConnection();
            }

            @Override
            public void onAdFailed() {
                // Ad failed, proceed with VPN connection anyway
                proceedWithVpnConnection();
            }
        });
    }

    private void proceedWithVpnConnection() {
        Intent intent = VpnService.prepare(this);
        if (intent != null) {
            startActivityForResult(intent, VPN_REQUEST_CODE);
        } else {
            // Already have permission
            vpnManager.connectVpn(selectedCountry);
        }
    }

    private void disconnectVpn() {
        vpnManager.disconnectVpn();
    }

    private void updateVpnUI() {
        if (vpnManager.isVpnConnected()) {
            String selectedServer = vpnManager.getSelectedServer();
            if (selectedServer == null || selectedServer.trim().isEmpty()) {
                selectedServer = "Unknown Server";
            }
            tvVpnConnectionStatus.setText("✅ Connected to " + selectedServer);
            tvVpnConnectionStatus.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
            btnSecure.setText("🔓 DISCONNECT");
            btnSecure.setBackgroundResource(R.drawable.disconnect_button_gradient);
            statusIndicator.setBackgroundResource(R.drawable.status_indicator_connected);
        } else {
            tvVpnConnectionStatus.setText("❌ Not Connected");
            tvVpnConnectionStatus.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
            btnSecure.setText("🔒 SECURE NOW");
            btnSecure.setBackgroundResource(R.drawable.secure_button_gradient);
            statusIndicator.setBackgroundResource(R.drawable.status_indicator_disconnected);
        }
    }

    public static void setWindowFlag(Activity activity, int i, boolean z) {
        Window window = activity.getWindow();
        WindowManager.LayoutParams attributes = window.getAttributes();
        if (z) {
            attributes.flags = i | attributes.flags;
        } else {
            attributes.flags = (~i) & attributes.flags;
        }
        window.setAttributes(attributes);
    }

    @Override
    public void onResume() {
        super.onResume();
        updateVpnUI();
    }

    @Override
    public void onBackPressed() {
        ExitDialog();
    }

    private void ExitDialog() {

        final Dialog dialog = new Dialog(MainActivity.this, R.style.DialogTheme);
        dialog.setContentView(R.layout.popup_exit_dialog);
        dialog.setCancelable(false);

        RelativeLayout no = (RelativeLayout) dialog.findViewById(R.id.no);
        RelativeLayout rate = (RelativeLayout) dialog.findViewById(R.id.rate);
        RelativeLayout yes = (RelativeLayout) dialog.findViewById(R.id.yes);

        no.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });

        rate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                final String rateapp = getPackageName();
                Intent intent1 = new Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id=" + rateapp));
                startActivity(intent1);
            }
        });

        yes.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
                finish();
                System.exit(0);
                //Intent intent = new Intent(AppMainHomeActivity.this, AppThankYouActivity.class);
                //intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                //AdsCommon.InterstitialAd(AppMainHomeActivity.this, intent);
            }
        });

        dialog.show();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == VPN_REQUEST_CODE && resultCode == RESULT_OK) {
            // Permission granted, connect to VPN
            vpnManager.connectVpn(selectedCountry);
        } else {
            Toast.makeText(this, "VPN permission denied", Toast.LENGTH_SHORT).show();
        }
    }

    // VpnManager.VpnConnectionListener implementation
    @Override
    public void onConnected() {
        runOnUiThread(() -> {
            // Stop any animations
            statusIndicator.clearAnimation();
            updateVpnUI();
            btnSecure.setEnabled(true);
            Toast.makeText(this, "VPN Connected Successfully! 🔒", Toast.LENGTH_SHORT).show();
        });
    }

    @Override
    public void onDisconnected() {
        runOnUiThread(() -> {
            // Stop any animations
            statusIndicator.clearAnimation();
            updateVpnUI();
            btnSecure.setEnabled(true);
            Toast.makeText(this, "VPN Disconnected 🔓", Toast.LENGTH_SHORT).show();
        });
    }

    @Override
    public void onConnecting() {
        runOnUiThread(() -> {
            tvVpnConnectionStatus.setText("⏳ Connecting...");
            tvVpnConnectionStatus.setTextColor(getResources().getColor(android.R.color.holo_orange_dark));
            btnSecure.setText("⏳ CONNECTING...");
            btnSecure.setEnabled(false);
            statusIndicator.setBackgroundResource(R.drawable.status_indicator_connecting);

            // Add pulse animation to status indicator while connecting
            Animation pulseAnimation = AnimationUtils.loadAnimation(this, R.anim.pulse_animation);
            statusIndicator.startAnimation(pulseAnimation);
        });
    }

    @Override
    public void onError(String error) {
        runOnUiThread(() -> {
            updateVpnUI();
            btnSecure.setEnabled(true);
            Toast.makeText(this, "VPN Error: " + error, Toast.LENGTH_LONG).show();
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (vpnAdManager != null) {
            vpnAdManager.cleanup();
        }
    }

}
