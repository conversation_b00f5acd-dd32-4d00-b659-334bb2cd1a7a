package com.demo.trackdevice.vpn;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.VpnService;
import android.util.Log;

import com.demo.trackdevice.vpn.model.Countries;

import java.util.ArrayList;
import java.util.List;

import java.io.IOException;

import top.oneconnectapi.app.OpenVpnApi;
import top.oneconnectapi.app.core.OpenVPNThread;

public class VpnManager {
    private static final String TAG = "VpnManager";
    private static final String PREFS_NAME = "vpn_prefs";
    private static final String KEY_VPN_CONNECTED = "vpn_connected";
    private static final String KEY_SELECTED_SERVER = "selected_server";

    private Context context;
    private SharedPreferences prefs;
    private VpnConnectionListener listener;
    private OpenVPNThread vpnThread;
    
    public interface VpnConnectionListener {
        void onConnected();
        void onDisconnected();
        void onConnecting();
        void onError(String error);
    }
    
    public VpnManager(Context context) {
        this.context = context;
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.vpnThread = new OpenVPNThread();
    }
    
    public void setConnectionListener(VpnConnectionListener listener) {
        this.listener = listener;
    }
    
    public boolean isVpnConnected() {
        // For now, just check our saved preference
        // In a full implementation, this would check the actual VPN service status
        return prefs.getBoolean(KEY_VPN_CONNECTED, false);
    }
    
    public void connectVpn(Countries server) {
        try {
            if (listener != null) {
                listener.onConnecting();
            }

            // Validate server data
            if (server == null) {
                throw new IllegalArgumentException("Server cannot be null");
            }

            String country = server.getCountry();
            String ovpn = server.getOvpn();
            String username = server.getOvpnUserName();
            String password = server.getOvpnUserPassword();

            if (country == null || country.trim().isEmpty()) {
                throw new IllegalArgumentException("Country name cannot be null or empty");
            }

            if (ovpn == null || ovpn.trim().isEmpty()) {
                throw new IllegalArgumentException("OVPN configuration cannot be null or empty");
            }

            // Save selected server
            prefs.edit().putString(KEY_SELECTED_SERVER, country).apply();

            // Disconnect any existing connection first
            if (vpnThread != null) {
                try {
                    vpnThread.stop();
                } catch (Exception e) {
                    Log.w(TAG, "Error stopping existing VPN: " + e.getMessage());
                }
            }

            // Start VPN connection using OneConnect API
            // Provide default values for username/password if null
            String safeUsername = (username != null && !username.trim().isEmpty()) ? username : "";
            String safePassword = (password != null && !password.trim().isEmpty()) ? password : "";

            // Create new VPN thread and start connection
            vpnThread = new OpenVPNThread();
            OpenVpnApi.startVpn(context, ovpn, country, safeUsername, safePassword);

            // Update connection status
            prefs.edit().putBoolean(KEY_VPN_CONNECTED, true).apply();

            if (listener != null) {
                listener.onConnected();
            }

            Log.d(TAG, "VPN connection started for: " + country);

        } catch (Exception e) {
            Log.e(TAG, "Error connecting VPN: " + e.getMessage());
            prefs.edit().putBoolean(KEY_VPN_CONNECTED, false).apply();
            if (listener != null) {
                listener.onError(e.getMessage() != null ? e.getMessage() : "Unknown VPN error");
            }
        }
    }
    
    public void disconnectVpn() {
        try {
            // Method 1: Use the DisconnectVPNActivity approach
            try {
                Intent disconnectIntent = new Intent(context, Class.forName("top.oneconnectapi.app.DisconnectVPNActivity"));
                disconnectIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(disconnectIntent);
                Log.d(TAG, "DisconnectVPNActivity started");
            } catch (ClassNotFoundException e) {
                Log.w(TAG, "DisconnectVPNActivity class not found, trying alternative method: " + e.getMessage());

                // Method 2: Try to stop the VPN service directly
                try {
                    Intent serviceIntent = new Intent(context, Class.forName("top.oneconnectapi.app.core.OpenVPNService"));
                    context.stopService(serviceIntent);
                    Log.d(TAG, "OpenVPN service stop intent sent");
                } catch (ClassNotFoundException serviceException) {
                    Log.w(TAG, "OpenVPNService class not found: " + serviceException.getMessage());
                }
            }

            // Clear the thread reference without calling stop()
            if (vpnThread != null) {
                vpnThread = null;
                Log.d(TAG, "VPN thread reference cleared");
            }

            // Update connection status
            prefs.edit().putBoolean(KEY_VPN_CONNECTED, false).apply();
            prefs.edit().remove(KEY_SELECTED_SERVER).apply();

            if (listener != null) {
                listener.onDisconnected();
            }

            Log.d(TAG, "VPN disconnected");

        } catch (Exception e) {
            Log.e(TAG, "Error disconnecting VPN: " + e.getMessage());
            // Still update the status even if there was an error
            prefs.edit().putBoolean(KEY_VPN_CONNECTED, false).apply();
            prefs.edit().remove(KEY_SELECTED_SERVER).apply();

            if (listener != null) {
                listener.onError(e.getMessage() != null ? e.getMessage() : "Unknown disconnection error");
            }
        }
    }
    
    public String getSelectedServer() {
        return prefs.getString(KEY_SELECTED_SERVER, "Not Connected");
    }
    
    public List<Countries> getAvailableServers() {
        List<Countries> servers = new ArrayList<>();
        
        // Add some default free servers (you can expand this list)
        servers.add(new Countries("United States", "🇺🇸", 
            "client\ndev tun\nproto udp\nremote us.example.com 1194\nresolv-retry infinite\nnobind\npersist-key\npersist-tun\nca ca.crt\ncert client.crt\nkey client.key\ncomp-lzo\nverb 3"));
        
        servers.add(new Countries("United Kingdom", "🇬🇧", 
            "client\ndev tun\nproto udp\nremote uk.example.com 1194\nresolv-retry infinite\nnobind\npersist-key\npersist-tun\nca ca.crt\ncert client.crt\nkey client.key\ncomp-lzo\nverb 3"));
        
        servers.add(new Countries("Germany", "🇩🇪", 
            "client\ndev tun\nproto udp\nremote de.example.com 1194\nresolv-retry infinite\nnobind\npersist-key\npersist-tun\nca ca.crt\ncert client.crt\nkey client.key\ncomp-lzo\nverb 3"));
        
        servers.add(new Countries("Japan", "🇯🇵", 
            "client\ndev tun\nproto udp\nremote jp.example.com 1194\nresolv-retry infinite\nnobind\npersist-key\npersist-tun\nca ca.crt\ncert client.crt\nkey client.key\ncomp-lzo\nverb 3"));
        
        return servers;
    }
}
