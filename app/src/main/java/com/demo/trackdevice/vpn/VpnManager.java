package com.demo.trackdevice.vpn;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import com.demo.trackdevice.vpn.model.Countries;

import java.util.ArrayList;
import java.util.List;

import top.oneconnectapi.app.OpenVpnApi;
import top.oneconnectapi.app.core.OpenVPNThread;

public class VpnManager {
    private static final String TAG = "VpnManager";
    private static final String PREFS_NAME = "vpn_prefs";
    private static final String KEY_VPN_CONNECTED = "vpn_connected";
    private static final String KEY_SELECTED_SERVER = "selected_server";
    
    private Context context;
    private SharedPreferences prefs;
    private VpnConnectionListener listener;
    
    public interface VpnConnectionListener {
        void onConnected();
        void onDisconnected();
        void onConnecting();
        void onError(String error);
    }
    
    public VpnManager(Context context) {
        this.context = context;
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }
    
    public void setConnectionListener(VpnConnectionListener listener) {
        this.listener = listener;
    }
    
    public boolean isVpnConnected() {
        return prefs.getBoolean(KEY_VPN_CONNECTED, false);
    }
    
    public void connectVpn(Countries server) {
        try {
            if (listener != null) {
                listener.onConnecting();
            }
            
            // Save selected server
            prefs.edit().putString(KEY_SELECTED_SERVER, server.getCountry()).apply();
            
            // Start VPN connection using OneConnect API
            OpenVpnApi.startVpn(context, server.getOvpn(), server.getCountry(), 
                server.getOvpnUserName(), server.getOvpnUserPassword());
            
            // Update connection status
            prefs.edit().putBoolean(KEY_VPN_CONNECTED, true).apply();
            
            if (listener != null) {
                listener.onConnected();
            }
            
            Log.d(TAG, "VPN connection started for: " + server.getCountry());
            
        } catch (Exception e) {
            Log.e(TAG, "Error connecting VPN: " + e.getMessage());
            if (listener != null) {
                listener.onError(e.getMessage());
            }
        }
    }
    
    public void disconnectVpn() {
        try {
            OpenVpnApi.stopVpn();
            prefs.edit().putBoolean(KEY_VPN_CONNECTED, false).apply();

            if (listener != null) {
                listener.onDisconnected();
            }

            Log.d(TAG, "VPN disconnected");

        } catch (Exception e) {
            Log.e(TAG, "Error disconnecting VPN: " + e.getMessage());
            if (listener != null) {
                listener.onError(e.getMessage());
            }
        }
    }
    
    public String getSelectedServer() {
        return prefs.getString(KEY_SELECTED_SERVER, "Not Connected");
    }
    
    public List<Countries> getAvailableServers() {
        List<Countries> servers = new ArrayList<>();
        
        // Add some default free servers (you can expand this list)
        servers.add(new Countries("United States", "🇺🇸", 
            "client\ndev tun\nproto udp\nremote us.example.com 1194\nresolv-retry infinite\nnobind\npersist-key\npersist-tun\nca ca.crt\ncert client.crt\nkey client.key\ncomp-lzo\nverb 3"));
        
        servers.add(new Countries("United Kingdom", "🇬🇧", 
            "client\ndev tun\nproto udp\nremote uk.example.com 1194\nresolv-retry infinite\nnobind\npersist-key\npersist-tun\nca ca.crt\ncert client.crt\nkey client.key\ncomp-lzo\nverb 3"));
        
        servers.add(new Countries("Germany", "🇩🇪", 
            "client\ndev tun\nproto udp\nremote de.example.com 1194\nresolv-retry infinite\nnobind\npersist-key\npersist-tun\nca ca.crt\ncert client.crt\nkey client.key\ncomp-lzo\nverb 3"));
        
        servers.add(new Countries("Japan", "🇯🇵", 
            "client\ndev tun\nproto udp\nremote jp.example.com 1194\nresolv-retry infinite\nnobind\npersist-key\npersist-tun\nca ca.crt\ncert client.crt\nkey client.key\ncomp-lzo\nverb 3"));
        
        return servers;
    }
}
