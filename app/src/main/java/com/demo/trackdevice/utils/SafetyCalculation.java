package com.demo.trackdevice.utils;

import android.content.Context;

public class SafetyCalculation {
    AppPreferences appPreferences;

    public int SafetyCalculationCount(Context context) {
        AppPreferences appPreferences2 = new AppPreferences(context);
        this.appPreferences = appPreferences2;
        int i = appPreferences2.getCameraPermissionTrusted() ? 12 : 0;
        if (this.appPreferences.getLocationPermissionTrusted()) {
            i += 12;
        }
        if (this.appPreferences.getStoragePermissionTrusted()) {
            i += 12;
        }
        if (this.appPreferences.getContactsPermissionTrusted()) {
            i += 12;
        }
        if (this.appPreferences.getMicPermissionTrusted()) {
            i += 12;
        }
        if (this.appPreferences.getNetworkPermissionTrusted()) {
            i += 12;
        }
        if (this.appPreferences.getNetworkSecurityTrusted()) {
            i += 14;
        }
        return this.appPreferences.getDeviceInfoTrusted() ? i + 14 : i;
    }
}
