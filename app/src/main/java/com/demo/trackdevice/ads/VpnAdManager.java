package com.demo.trackdevice.ads;

import android.app.Activity;
import android.app.Dialog;
import android.os.CountDownTimer;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.demo.trackdevice.R;
import com.demo.trackdevice.ads.admob.AdMobInterstitialClick;
import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.LoadAdError;

public class VpnAdManager {
    private static final String TAG = "VpnAdManager";
    private static final int COUNTDOWN_DURATION = 5; // 5 seconds
    
    private Activity activity;
    private Dialog adDialog;
    private CountDownTimer countDownTimer;
    private VpnAdCallback callback;
    private TextView tvTimer;
    private Button btnSkip;
    private FrameLayout adContainer;
    private LinearLayout adPlaceholder;
    private boolean adLoaded = false;
    
    public interface VpnAdCallback {
        void onAdCompleted();
        void onAdSkipped();
        void onAdFailed();
    }
    
    public VpnAdManager(Activity activity) {
        this.activity = activity;
    }
    
    public void showVpnAd(VpnAdCallback callback) {
        this.callback = callback;

        Log.d(TAG, "showVpnAd called, user_balance: " + MyApplication.getuser_balance());

        // Check if ads are enabled
        if (MyApplication.getuser_balance() != 0) {
            // User has premium, skip ad
            Log.d(TAG, "User has premium, skipping ad");
            if (callback != null) {
                callback.onAdCompleted();
            }
            return;
        }

        Log.d(TAG, "Creating and showing ad dialog");
        createAndShowDialog();
    }
    
    private void createAndShowDialog() {
        try {
            Log.d(TAG, "Creating dialog...");
            adDialog = new Dialog(activity);
            adDialog.setContentView(R.layout.vpn_ad_dialog);
            adDialog.setCancelable(false);

            // Initialize views
            tvTimer = adDialog.findViewById(R.id.tv_timer);
            btnSkip = adDialog.findViewById(R.id.btn_skip);
            adContainer = adDialog.findViewById(R.id.ad_container);
            adPlaceholder = adDialog.findViewById(R.id.ad_placeholder);

            Log.d(TAG, "Views initialized, setting up skip button");

            // Setup skip button
            btnSkip.setOnClickListener(v -> {
                Log.d(TAG, "Skip button clicked");
                dismissDialog();
                if (callback != null) {
                    callback.onAdSkipped();
                }
            });

            // Show dialog
            Log.d(TAG, "Showing dialog...");
            adDialog.show();

            // Load ad
            loadAd();

            // Start countdown
            startCountdown();

        } catch (Exception e) {
            Log.e(TAG, "Error creating dialog: " + e.getMessage());
            if (callback != null) {
                callback.onAdFailed();
            }
        }
    }
    
    private void loadAd() {
        try {
            // Try to load a banner ad for the container
            if (MyApplication.Type1.contains("admob")) {
                loadAdMobBanner();
            } else {
                // If no ad type configured, just show placeholder
                Log.d(TAG, "No ad type configured, showing placeholder");
                adLoaded = true;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error loading ad: " + e.getMessage());
            adLoaded = true; // Continue with placeholder
        }
    }
    
    private void loadAdMobBanner() {
        try {
            AdView adView = new AdView(activity);
            adView.setAdSize(com.google.android.gms.ads.AdSize.MEDIUM_RECTANGLE);
            adView.setAdUnitId(MyApplication.AdMob_Banner1);
            
            adView.setAdListener(new AdListener() {
                @Override
                public void onAdLoaded() {
                    Log.d(TAG, "Banner ad loaded successfully");
                    adLoaded = true;
                    // Hide placeholder and show ad
                    if (adPlaceholder != null) {
                        adPlaceholder.setVisibility(View.GONE);
                    }
                }
                
                @Override
                public void onAdFailedToLoad(LoadAdError adError) {
                    Log.e(TAG, "Banner ad failed to load: " + adError.getMessage());
                    adLoaded = true;
                    // Keep placeholder visible
                }
            });
            
            // Add ad to container
            adContainer.removeAllViews();
            adContainer.addView(adView);
            
            // Load the ad
            AdRequest adRequest = new AdRequest.Builder().build();
            adView.loadAd(adRequest);
            
        } catch (Exception e) {
            Log.e(TAG, "Error creating AdMob banner: " + e.getMessage());
            adLoaded = true;
        }
    }
    
    private void startCountdown() {
        countDownTimer = new CountDownTimer(COUNTDOWN_DURATION * 1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                int secondsLeft = (int) (millisUntilFinished / 1000);
                
                // Update timer display
                if (tvTimer != null) {
                    tvTimer.setText(secondsLeft + "s");
                }
                
                // Update skip button
                if (btnSkip != null) {
                    btnSkip.setText("Skip (" + secondsLeft + "s)");
                }
            }
            
            @Override
            public void onFinish() {
                // Enable skip button
                if (btnSkip != null) {
                    btnSkip.setText("🚀 CONNECT NOW");
                    btnSkip.setEnabled(true);
                    btnSkip.setAlpha(1.0f);
                }
                
                if (tvTimer != null) {
                    tvTimer.setText("✓");
                }
                
                Log.d(TAG, "Countdown finished, skip button enabled");
            }
        };
        
        countDownTimer.start();
    }
    
    private void dismissDialog() {
        try {
            if (countDownTimer != null) {
                countDownTimer.cancel();
                countDownTimer = null;
            }
            
            if (adDialog != null && adDialog.isShowing()) {
                adDialog.dismiss();
                adDialog = null;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error dismissing dialog: " + e.getMessage());
        }
    }
    
    public void cleanup() {
        dismissDialog();
        callback = null;
    }
}
